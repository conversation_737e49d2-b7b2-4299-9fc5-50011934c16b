const express = require('express');
const router = express.Router();
const DemandForecast = require('../models/DemandForecast');
const ReorderSuggestion = require('../models/ReorderSuggestion');
const SalesVelocity = require('../models/SalesVelocity');
const ProphetForecastingService = require('../services/prophetForecastingService');
const ForecastScheduler = require('../services/forecastScheduler');
const InstacartDataSyncService = require('../services/instacartDataSync');
const { auth } = require('../middleware/auth');

const forecastingService = new ProphetForecastingService();
const forecastScheduler = new ForecastScheduler();
const dataSyncService = new InstacartDataSyncService();

// Comprehensive forecast analysis function
async function generateComprehensiveForecastAnalysis(forecasts, season = null) {
  try {
    const Product = require('../models/Product');
    const Supplier = require('../models/Supplier');
    const InstacartProduct = require('../models/InstacartProduct');
    const InstacartDepartment = require('../models/InstacartDepartment');
    const InstacartAisle = require('../models/InstacartAisle');

    // Get all products with their suppliers
    const products = await Product.find({}).populate('supplier');
    const productMap = new Map(products.map(p => [p._id.toString(), p]));

    // Seasonal patterns
    const seasonalPatterns = {
      'fresh fruits': { peak: [6, 7, 8], low: [12, 1, 2] },
      'fresh vegetables': { peak: [6, 7, 8, 9], low: [12, 1, 2] },
      'dairy eggs': { peak: [11, 12, 1], low: [6, 7, 8] },
      'snacks': { peak: [11, 12, 1, 7, 8], low: [2, 3, 4] },
      'beverages': { peak: [6, 7, 8], low: [1, 2, 3] },
      'frozen': { peak: [11, 12, 1, 2], low: [6, 7, 8] },
      'pantry': { peak: [11, 12, 1], low: [4, 5, 6] },
      'personal care': { peak: [1, 6, 7], low: [3, 4, 5] },
      'household': { peak: [3, 4, 9, 10], low: [6, 7, 8] }
    };

    const currentMonth = new Date().getMonth() + 1;

    // Analyze forecasts
    const lowStockProducts = [];
    const highDemandProducts = [];
    const seasonalRecommendations = [];
    const supplierAnalysis = new Map();

    for (const forecast of forecasts) {
      const product = productMap.get(forecast.product_id.toString());
      if (!product) continue;

      // Calculate total predicted demand
      const totalDemand = forecast.forecast_data.reduce((sum, point) => sum + point.predicted_demand, 0);
      const avgDailyDemand = totalDemand / forecast.forecast_data.length;

      // Check for low stock
      const daysOfStock = product.quantity / Math.max(avgDailyDemand, 1);
      if (daysOfStock < 14) {
        lowStockProducts.push({
          product_id: product._id,
          name: product.name,
          sku: product.sku,
          current_stock: product.quantity,
          days_of_stock: Math.round(daysOfStock),
          avg_daily_demand: Math.round(avgDailyDemand),
          total_predicted_demand: Math.round(totalDemand),
          urgency: daysOfStock < 7 ? 'Critical' : daysOfStock < 14 ? 'High' : 'Medium',
          supplier: product.supplier ? {
            name: product.supplier.name,
            contact: product.supplier.contact_email,
            lead_time: product.supplier.lead_time_days || 7
          } : null
        });
      }

      // Check for high demand
      if (avgDailyDemand > 10) {
        highDemandProducts.push({
          product_id: product._id,
          name: product.name,
          sku: product.sku,
          avg_daily_demand: Math.round(avgDailyDemand),
          total_predicted_demand: Math.round(totalDemand),
          category: product.category
        });
      }

      // Seasonal analysis
      const categoryLower = product.category.toLowerCase();
      const matchedPattern = Object.keys(seasonalPatterns).find(pattern =>
        categoryLower.includes(pattern.replace(' ', '')) ||
        categoryLower.includes(pattern.split(' ')[0])
      );

      if (matchedPattern) {
        const pattern = seasonalPatterns[matchedPattern];
        const isPeakSeason = pattern.peak.includes(currentMonth);
        const isLowSeason = pattern.low.includes(currentMonth);

        if (season && season.toLowerCase() === 'peak' && isPeakSeason) {
          seasonalRecommendations.push({
            product_id: product._id,
            name: product.name,
            category: product.category,
            season_type: 'peak',
            recommendation: `Increase stock by 30-50% for peak season`,
            suggested_quantity: Math.round(totalDemand * 1.4),
            current_stock: product.quantity
          });
        } else if (season && season.toLowerCase() === 'low' && isLowSeason) {
          seasonalRecommendations.push({
            product_id: product._id,
            name: product.name,
            category: product.category,
            season_type: 'low',
            recommendation: `Reduce stock by 20-30% for low season`,
            suggested_quantity: Math.round(totalDemand * 0.7),
            current_stock: product.quantity
          });
        }
      }

      // Supplier analysis
      if (product.supplier) {
        const supplierId = product.supplier._id.toString();
        if (!supplierAnalysis.has(supplierId)) {
          supplierAnalysis.set(supplierId, {
            supplier: {
              id: product.supplier._id,
              name: product.supplier.name,
              contact: product.supplier.contact_email,
              phone: product.supplier.contact_phone,
              lead_time: product.supplier.lead_time_days || 7
            },
            products: [],
            total_demand: 0,
            total_value: 0
          });
        }

        const supplierData = supplierAnalysis.get(supplierId);
        supplierData.products.push({
          name: product.name,
          sku: product.sku,
          predicted_demand: Math.round(totalDemand),
          current_stock: product.quantity,
          unit_price: product.price
        });
        supplierData.total_demand += totalDemand;
        supplierData.total_value += totalDemand * product.price;
      }
    }

    return {
      summary: {
        total_products_analyzed: forecasts.length,
        low_stock_alerts: lowStockProducts.length,
        high_demand_products: highDemandProducts.length,
        seasonal_recommendations: seasonalRecommendations.length,
        suppliers_involved: supplierAnalysis.size
      },
      low_stock_products: lowStockProducts.sort((a, b) => a.days_of_stock - b.days_of_stock),
      high_demand_products: highDemandProducts.sort((a, b) => b.avg_daily_demand - a.avg_daily_demand),
      seasonal_recommendations: seasonalRecommendations,
      supplier_analysis: Array.from(supplierAnalysis.values()).sort((a, b) => b.total_value - a.total_value),
      generated_at: new Date()
    };
  } catch (error) {
    console.error('Error generating comprehensive analysis:', error);
    throw error;
  }
}

// Generate comprehensive demand forecasts with full analysis
router.post('/forecasts/generate', auth, async (req, res) => {
  try {
    const { productIds, forecastDays = 30, season = null } = req.body;

    console.log('Generating comprehensive demand forecasts...', { productIds, forecastDays, season });

    // Generate basic forecasts
    const forecasts = await forecastingService.generateDemandForecasts(productIds, {
      forecastDays
    });

    // Generate comprehensive analysis
    const comprehensiveAnalysis = await generateComprehensiveForecastAnalysis(forecasts, season);

    res.json({
      success: true,
      message: `Generated comprehensive forecasts for ${forecasts.length} products`,
      forecasts: forecasts.map(f => ({
        id: f._id,
        product_name: f.product_name,
        sku: f.sku,
        forecast_horizon_days: f.forecast_horizon_days,
        status: f.status,
        generated_at: f.forecast_generated_at
      })),
      analysis: comprehensiveAnalysis
    });
  } catch (error) {
    console.error('Error generating forecasts:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Get demand forecasts with filtering and pagination
router.get('/forecasts', auth, async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 20, 
      category, 
      status = 'active',
      sku,
      sortBy = 'forecast_generated_at',
      sortOrder = 'desc'
    } = req.query;
    
    const query = { status };
    if (category) query.category = category;
    if (sku) query.sku = new RegExp(sku, 'i');
    
    const sort = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;
    
    const forecasts = await DemandForecast.find(query)
      .populate('product_id', 'name category quantity minStock')
      .sort(sort)
      .limit(limit * 1)
      .skip((page - 1) * limit);
    
    const total = await DemandForecast.countDocuments(query);
    
    res.json({
      forecasts,
      pagination: {
        current_page: parseInt(page),
        total_pages: Math.ceil(total / limit),
        total_items: total,
        items_per_page: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('Error fetching forecasts:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get specific demand forecast by ID
router.get('/forecasts/:id', auth, async (req, res) => {
  try {
    const forecast = await DemandForecast.findById(req.params.id)
      .populate('product_id', 'name category quantity minStock maxStock supplier');
    
    if (!forecast) {
      return res.status(404).json({ error: 'Forecast not found' });
    }
    
    res.json(forecast);
  } catch (error) {
    console.error('Error fetching forecast:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get forecast data for specific product
router.get('/forecasts/product/:productId', auth, async (req, res) => {
  try {
    const { days = 30 } = req.query;
    
    const forecast = await DemandForecast.findOne({
      product_id: req.params.productId,
      status: 'active'
    }).populate('product_id', 'name category quantity');
    
    if (!forecast) {
      return res.status(404).json({ error: 'No active forecast found for this product' });
    }
    
    // Get forecast data for specified number of days
    const startDate = new Date();
    const endDate = new Date();
    endDate.setDate(endDate.getDate() + parseInt(days));
    
    const forecastData = forecast.getForecastForDateRange(startDate, endDate);
    
    res.json({
      product: forecast.product_id,
      forecast_info: {
        model_type: forecast.model_type,
        generated_at: forecast.forecast_generated_at,
        horizon_days: forecast.forecast_horizon_days
      },
      forecast_data: forecastData,
      summary: {
        avg_daily_demand: forecast.getAveragePredictedDemand(parseInt(days)),
        total_predicted_demand: forecastData.reduce((sum, point) => sum + point.predicted_demand, 0)
      }
    });
  } catch (error) {
    console.error('Error fetching product forecast:', error);
    res.status(500).json({ error: error.message });
  }
});

// Generate reorder suggestions
router.post('/reorder-suggestions/generate', auth, async (req, res) => {
  try {
    const { forecastIds } = req.body;
    
    console.log('Generating reorder suggestions...', { forecastIds });
    
    const suggestions = await forecastingService.generateReorderSuggestions(forecastIds);
    
    res.json({
      success: true,
      message: `Generated ${suggestions.length} reorder suggestions`,
      suggestions: suggestions.map(s => ({
        id: s._id,
        product_name: s.product_name,
        sku: s.sku,
        urgency_level: s.urgency_level,
        suggested_quantity: s.suggested_reorder_quantity,
        stockout_risk: s.stockout_risk_percentage
      }))
    });
  } catch (error) {
    console.error('Error generating reorder suggestions:', error);
    res.status(500).json({ 
      success: false, 
      error: error.message 
    });
  }
});

// Get reorder suggestions with filtering
router.get('/reorder-suggestions', auth, async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 20, 
      urgency, 
      status = 'pending',
      sortBy = 'stockout_risk_percentage',
      sortOrder = 'desc'
    } = req.query;
    
    const query = { status };
    if (urgency) query.urgency_level = urgency;
    
    const sort = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;
    
    const suggestions = await ReorderSuggestion.find(query)
      .populate('product_id', 'name category supplier')
      .sort(sort)
      .limit(limit * 1)
      .skip((page - 1) * limit);
    
    const total = await ReorderSuggestion.countDocuments(query);
    
    res.json({
      suggestions,
      pagination: {
        current_page: parseInt(page),
        total_pages: Math.ceil(total / limit),
        total_items: total,
        items_per_page: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('Error fetching reorder suggestions:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get high priority reorder suggestions for dashboard
router.get('/reorder-suggestions/high-priority', auth, async (req, res) => {
  try {
    const { limit = 10 } = req.query;
    
    const suggestions = await ReorderSuggestion.getHighPrioritySuggestions(parseInt(limit));
    
    res.json(suggestions);
  } catch (error) {
    console.error('Error fetching high priority suggestions:', error);
    res.status(500).json({ error: error.message });
  }
});

// Update reorder suggestion status
router.patch('/reorder-suggestions/:id', auth, async (req, res) => {
  try {
    const { status, review_notes } = req.body;
    
    const suggestion = await ReorderSuggestion.findById(req.params.id);
    if (!suggestion) {
      return res.status(404).json({ error: 'Reorder suggestion not found' });
    }
    
    suggestion.status = status;
    if (review_notes) suggestion.review_notes = review_notes;
    suggestion.reviewed_by = req.user.id;
    suggestion.reviewed_at = new Date();
    
    await suggestion.save();
    
    res.json({
      success: true,
      message: 'Reorder suggestion updated successfully',
      suggestion
    });
  } catch (error) {
    console.error('Error updating reorder suggestion:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get velocity analytics
router.get('/velocity/analytics', auth, async (req, res) => {
  try {
    const { days = 30, category } = req.query;
    
    // Get top velocity products
    const topVelocityProducts = await SalesVelocity.getTopVelocityProducts(10, parseInt(days));
    
    // Get velocity trends by category
    const velocityTrends = await SalesVelocity.aggregate([
      {
        $match: {
          date: { $gte: new Date(Date.now() - days * 24 * 60 * 60 * 1000) },
          period_type: 'daily'
        }
      },
      {
        $group: {
          _id: '$category',
          avg_velocity: { $avg: '$velocity_units_per_day' },
          total_units: { $sum: '$units_sold' },
          product_count: { $addToSet: '$product_id' }
        }
      },
      {
        $project: {
          category: '$_id',
          avg_velocity: { $round: ['$avg_velocity', 2] },
          total_units: 1,
          product_count: { $size: '$product_count' }
        }
      },
      { $sort: { avg_velocity: -1 } }
    ]);
    
    res.json({
      top_velocity_products: topVelocityProducts,
      velocity_trends_by_category: velocityTrends,
      analysis_period_days: parseInt(days)
    });
  } catch (error) {
    console.error('Error fetching velocity analytics:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get dashboard summary for predictive analytics
router.get('/dashboard/summary', auth, async (req, res) => {
  try {
    const [
      totalForecasts,
      activeForecasts,
      pendingSuggestions,
      highPrioritySuggestions,
      criticalSuggestions
    ] = await Promise.all([
      DemandForecast.countDocuments(),
      DemandForecast.countDocuments({ status: 'active' }),
      ReorderSuggestion.countDocuments({ status: 'pending' }),
      ReorderSuggestion.countDocuments({ 
        status: 'pending', 
        urgency_level: { $in: ['High', 'Critical'] } 
      }),
      ReorderSuggestion.countDocuments({ 
        status: 'pending', 
        urgency_level: 'Critical' 
      })
    ]);
    
    res.json({
      forecasts: {
        total: totalForecasts,
        active: activeForecasts
      },
      reorder_suggestions: {
        pending: pendingSuggestions,
        high_priority: highPrioritySuggestions,
        critical: criticalSuggestions
      },
      last_updated: new Date()
    });
  } catch (error) {
    console.error('Error fetching dashboard summary:', error);
    res.status(500).json({ error: error.message });
  }
});

// Scheduler management endpoints

// Get scheduler status
router.get('/scheduler/status', auth, async (req, res) => {
  try {
    const status = forecastScheduler.getStatus();
    res.json({
      scheduler_status: status,
      server_time: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error getting scheduler status:', error);
    res.status(500).json({ error: error.message });
  }
});

// Manually trigger a scheduled job
router.post('/scheduler/trigger/:jobName', auth, async (req, res) => {
  try {
    const { jobName } = req.params;

    console.log(`Manually triggering job: ${jobName}`);
    await forecastScheduler.triggerJob(jobName);

    res.json({
      success: true,
      message: `Job ${jobName} triggered successfully`,
      triggered_at: new Date().toISOString()
    });
  } catch (error) {
    console.error(`Error triggering job ${req.params.jobName}:`, error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Data synchronization endpoints

// Get Instacart data sync status
router.get('/sync/status', auth, async (req, res) => {
  try {
    const status = await dataSyncService.getSyncStatus();
    res.json(status);
  } catch (error) {
    console.error('Error getting sync status:', error);
    res.status(500).json({ error: error.message });
  }
});

// Sync product catalog from Instacart
router.post('/sync/products', auth, async (req, res) => {
  try {
    console.log('Starting product catalog sync...');
    const result = await dataSyncService.syncProductCatalog();

    res.json({
      success: true,
      message: 'Product catalog sync completed',
      result
    });
  } catch (error) {
    console.error('Error syncing product catalog:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Sync sales velocity data from Instacart
router.post('/sync/velocity', auth, async (req, res) => {
  try {
    console.log('Starting sales velocity sync...');
    const result = await dataSyncService.syncSalesVelocity();

    res.json({
      success: true,
      message: 'Sales velocity sync completed',
      result
    });
  } catch (error) {
    console.error('Error syncing sales velocity:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Full synchronization
router.post('/sync/full', auth, async (req, res) => {
  try {
    console.log('Starting full data synchronization...');
    const result = await dataSyncService.fullSync();

    res.json({
      success: true,
      message: 'Full synchronization completed',
      result
    });
  } catch (error) {
    console.error('Error during full sync:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Get comprehensive forecast dashboard data
router.get('/dashboard/comprehensive', auth, async (req, res) => {
  try {
    const { season, days = 30 } = req.query;

    // Get recent forecasts
    const recentForecasts = await DemandForecast.find({
      status: 'active',
      forecast_generated_at: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) }
    }).limit(50);

    if (recentForecasts.length === 0) {
      return res.json({
        success: true,
        message: 'No recent forecasts available. Generate new forecasts to see comprehensive analysis.',
        analysis: null
      });
    }

    // Generate comprehensive analysis
    const analysis = await generateComprehensiveForecastAnalysis(recentForecasts, season);

    res.json({
      success: true,
      analysis
    });
  } catch (error) {
    console.error('Error fetching comprehensive dashboard:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Get seasonal recommendations
router.get('/seasonal-analysis', auth, async (req, res) => {
  try {
    const { season, category } = req.query;
    const currentMonth = new Date().getMonth() + 1;

    // Seasonal patterns
    const seasonalPatterns = {
      'fresh fruits': { peak: [6, 7, 8], low: [12, 1, 2], description: 'Summer peak, winter low' },
      'fresh vegetables': { peak: [6, 7, 8, 9], low: [12, 1, 2], description: 'Summer/fall peak, winter low' },
      'dairy eggs': { peak: [11, 12, 1], low: [6, 7, 8], description: 'Holiday baking season peak' },
      'snacks': { peak: [11, 12, 1, 7, 8], low: [2, 3, 4], description: 'Holidays and summer peak' },
      'beverages': { peak: [6, 7, 8], low: [1, 2, 3], description: 'Summer peak' },
      'frozen': { peak: [11, 12, 1, 2], low: [6, 7, 8], description: 'Winter peak' },
      'pantry': { peak: [11, 12, 1], low: [4, 5, 6], description: 'Holiday cooking peak' },
      'personal care': { peak: [1, 6, 7], low: [3, 4, 5], description: 'New Year and summer peak' },
      'household': { peak: [3, 4, 9, 10], low: [6, 7, 8], description: 'Spring cleaning and back-to-school' }
    };

    const seasonalData = Object.entries(seasonalPatterns).map(([cat, pattern]) => ({
      category: cat,
      current_season: pattern.peak.includes(currentMonth) ? 'peak' :
                     pattern.low.includes(currentMonth) ? 'low' : 'normal',
      peak_months: pattern.peak,
      low_months: pattern.low,
      description: pattern.description,
      recommendation: pattern.peak.includes(currentMonth) ?
        'Increase inventory by 30-50%' :
        pattern.low.includes(currentMonth) ?
        'Reduce inventory by 20-30%' :
        'Maintain normal inventory levels'
    }));

    res.json({
      success: true,
      current_month: currentMonth,
      seasonal_patterns: seasonalData,
      filtered_category: category ? seasonalData.filter(s =>
        s.category.toLowerCase().includes(category.toLowerCase())
      ) : null
    });
  } catch (error) {
    console.error('Error fetching seasonal analysis:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Get supplier recommendations based on forecasts
router.get('/supplier-recommendations', auth, async (req, res) => {
  try {
    const { urgency = 'all' } = req.query;

    // Get recent reorder suggestions
    const suggestions = await ReorderSuggestion.find({
      status: 'pending',
      ...(urgency !== 'all' && { urgency_level: urgency })
    }).populate('product_id');

    // Group by supplier
    const supplierRecommendations = new Map();

    for (const suggestion of suggestions) {
      if (!suggestion.product_id || !suggestion.product_id.supplier) continue;

      const supplier = suggestion.product_id.supplier;
      const supplierId = supplier._id.toString();

      if (!supplierRecommendations.has(supplierId)) {
        supplierRecommendations.set(supplierId, {
          supplier: {
            id: supplier._id,
            name: supplier.name,
            contact_email: supplier.contact_email,
            contact_phone: supplier.contact_phone,
            lead_time_days: supplier.lead_time_days || 7
          },
          products: [],
          total_quantity: 0,
          total_value: 0,
          urgency_breakdown: { Critical: 0, High: 0, Normal: 0, Low: 0 }
        });
      }

      const supplierData = supplierRecommendations.get(supplierId);
      supplierData.products.push({
        name: suggestion.product_name,
        sku: suggestion.sku,
        suggested_quantity: suggestion.suggested_reorder_quantity,
        urgency: suggestion.urgency_level,
        stockout_risk: suggestion.stockout_risk_percentage,
        days_until_stockout: suggestion.days_until_stockout,
        estimated_value: suggestion.suggested_reorder_quantity * (suggestion.product_id.price || 10)
      });

      supplierData.total_quantity += suggestion.suggested_reorder_quantity;
      supplierData.total_value += suggestion.suggested_reorder_quantity * (suggestion.product_id.price || 10);
      supplierData.urgency_breakdown[suggestion.urgency_level]++;
    }

    const recommendations = Array.from(supplierRecommendations.values())
      .sort((a, b) => b.total_value - a.total_value);

    res.json({
      success: true,
      supplier_recommendations: recommendations,
      summary: {
        total_suppliers: recommendations.length,
        total_products: suggestions.length,
        total_estimated_value: recommendations.reduce((sum, r) => sum + r.total_value, 0)
      }
    });
  } catch (error) {
    console.error('Error fetching supplier recommendations:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

module.exports = router;
